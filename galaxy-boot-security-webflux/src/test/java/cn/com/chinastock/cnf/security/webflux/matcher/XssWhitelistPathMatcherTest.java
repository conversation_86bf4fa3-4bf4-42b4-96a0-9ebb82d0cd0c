package cn.com.chinastock.cnf.security.webflux.matcher;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class XssWhitelistPathMatcherTest {

    private XssWhitelistPathMatcher pathMatcher;

    @BeforeEach
    void setUp() {
        List<String> whitelistPatterns = Arrays.asList(
                "/api/content/**",
                "/public/**",
                "/health",
                "/swagger-ui/**",
                "/v3/api-docs/**"
        );
        pathMatcher = new XssWhitelistPathMatcher(whitelistPatterns);
    }

    @Test
    void shouldMatchExactPath() {
        assertTrue(pathMatcher.isWhitelisted("/health"));
    }

    @Test
    void shouldMatchWildcardPattern() {
        assertTrue(pathMatcher.isWhitelisted("/api/content/articles"));
        assertTrue(pathMatcher.isWhitelisted("/api/content/articles/123"));
        assertTrue(pathMatcher.isWhitelisted("/public/images/logo.png"));
        assertTrue(pathMatcher.isWhitelisted("/swagger-ui/index.html"));
        assertTrue(pathMatcher.isWhitelisted("/v3/api-docs/swagger-config"));
    }

    @Test
    void shouldNotMatchNonWhitelistedPath() {
        assertFalse(pathMatcher.isWhitelisted("/api/admin/users"));
        assertFalse(pathMatcher.isWhitelisted("/private/data"));
        assertFalse(pathMatcher.isWhitelisted("/api/sensitive"));
    }

    @Test
    void shouldReturnFalseForNullPath() {
        assertFalse(pathMatcher.isWhitelisted(null));
    }

    @Test
    void shouldReturnFalseForEmptyWhitelist() {
        XssWhitelistPathMatcher emptyMatcher = new XssWhitelistPathMatcher(Collections.emptyList());
        assertFalse(emptyMatcher.isWhitelisted("/any/path"));
    }

    @Test
    void shouldReturnFalseForNullWhitelist() {
        XssWhitelistPathMatcher nullMatcher = new XssWhitelistPathMatcher(null);
        assertFalse(nullMatcher.isWhitelisted("/any/path"));
    }

    @Test
    void shouldHandleComplexPatterns() {
        List<String> complexPatterns = Arrays.asList(
                "/api/*/public/**",
                "/files/*.{jpg,png,gif}",
                "/admin/user?/profile"
        );
        XssWhitelistPathMatcher complexMatcher = new XssWhitelistPathMatcher(complexPatterns);
        
        assertTrue(complexMatcher.isWhitelisted("/api/v1/public/data"));
        assertTrue(complexMatcher.isWhitelisted("/api/v2/public/files/document.pdf"));
    }

    @Test
    void shouldReturnWhitelistPatterns() {
        List<String> patterns = pathMatcher.getWhitelistPatterns();
        assertNotNull(patterns);
        assertEquals(5, patterns.size());
        assertTrue(patterns.contains("/api/content/**"));
        assertTrue(patterns.contains("/health"));
    }
}
