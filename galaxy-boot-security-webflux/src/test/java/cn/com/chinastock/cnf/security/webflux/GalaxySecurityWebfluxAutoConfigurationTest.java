package cn.com.chinastock.cnf.security.webflux;

import cn.com.chinastock.cnf.security.GalaxySecurityProperties;
import cn.com.chinastock.cnf.security.output.CopiedFastJsonProperties;
import cn.com.chinastock.cnf.security.webflux.filter.XssWebFilter;
import cn.com.chinastock.cnf.security.webflux.handler.XssResponseBodyAdvice;
import cn.com.chinastock.cnf.security.webflux.matcher.XssWhitelistPathMatcher;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ReactiveWebApplicationContextRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.web.reactive.accept.RequestedContentTypeResolver;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Galaxy Security WebFlux 自动配置测试
 * 
 * 
 */
class GalaxySecurityWebfluxAutoConfigurationTest {

    private final ReactiveWebApplicationContextRunner contextRunner = new ReactiveWebApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(GalaxySecurityWebfluxAutoConfiguration.class));

    @Test
    void shouldCreateSecurityWebFilterChain() {
        contextRunner.run(context -> {
            assertThat(context).hasSingleBean(SecurityWebFilterChain.class);
            assertThat(context).hasSingleBean(PasswordEncoder.class);
        });
    }

    @Test
    void shouldCreateXssResponseBodyAdviceWhenOutputProtectEnabled() {
        contextRunner
                .withUserConfiguration(TestWebFluxConfiguration.class)
                .withPropertyValues("galaxy.security.output-protect=true")
                .run(context -> {
                    assertThat(context).hasSingleBean(XssResponseBodyAdvice.class);
                });
    }

    @Test
    void shouldNotCreateXssResponseBodyAdviceWhenOutputProtectDisabled() {
        contextRunner
                .withUserConfiguration(TestWebFluxConfiguration.class)
                .withPropertyValues("galaxy.security.output-protect=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(XssResponseBodyAdvice.class);
                });
    }

    @Test
    void shouldCreateXssWebFilterWhenInputProtectEnabled() {
        contextRunner
                .withPropertyValues("galaxy.security.input-protect=true")
                .run(context -> {
                    assertThat(context).hasSingleBean(XssWebFilter.class);
                    assertThat(context).hasSingleBean(XssWhitelistPathMatcher.class);
                });
    }

    @Test
    void shouldNotCreateXssWebFilterWhenInputProtectDisabled() {
        contextRunner
                .withPropertyValues("galaxy.security.input-protect=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(XssWebFilter.class);
                    assertThat(context).doesNotHaveBean(XssWhitelistPathMatcher.class);
                });
    }

    @Test
    void shouldLoadPropertiesCorrectly() {
        contextRunner
                .withPropertyValues(
                        "galaxy.security.input-protect=true",
                        "galaxy.security.output-protect=true",
                        "galaxy.security.user.name=testuser",
                        "galaxy.security.user.password=testpass",
                        "galaxy.security.cors.protect=true",
                        "galaxy.security.csrf.protect=true",
                        "galaxy.security.xss.whitelist-patterns[0]=/api/content/**",
                        "galaxy.security.xss.whitelist-patterns[1]=/public/**"
                )
                .run(context -> {
                    GalaxySecurityProperties properties = context.getBean(GalaxySecurityProperties.class);
                    assertThat(properties.isInputProtect()).isTrue();
                    assertThat(properties.isOutputProtect()).isTrue();
                    assertThat(properties.getUser().getName()).isEqualTo("testuser");
                    assertThat(properties.getUser().getPassword()).isEqualTo("testpass");
                    assertThat(properties.getCors().isProtect()).isTrue();
                    assertThat(properties.getCsrf().isProtect()).isTrue();
                    assertThat(properties.getXss().getWhitelistPatterns()).hasSize(2);
                    assertThat(properties.getXss().getWhitelistPatterns()).contains("/api/content/**", "/public/**");
                });
    }

    @Test
    void shouldCreateReactiveUserDetailsServiceWhenUserConfigured() {
        contextRunner
                .withPropertyValues(
                        "galaxy.security.user.name=testuser",
                        "galaxy.security.user.password=testpass"
                )
                .run(context -> {
                    assertThat(context).hasSingleBean(org.springframework.security.core.userdetails.ReactiveUserDetailsService.class);
                });
    }

    @Test
    void shouldNotCreateReactiveUserDetailsServiceWhenUserNotConfigured() {
        contextRunner
                .run(context -> {
                    assertThat(context).doesNotHaveBean(org.springframework.security.core.userdetails.ReactiveUserDetailsService.class);
                });
    }

    @Test
    void shouldConfigureXssWhitelistCorrectly() {
        contextRunner
                .withPropertyValues(
                        "galaxy.security.input-protect=true",
                        "galaxy.security.xss.whitelist-patterns[0]=/api/content/**",
                        "galaxy.security.xss.whitelist-patterns[1]=/swagger-ui/**"
                )
                .run(context -> {
                    XssWhitelistPathMatcher matcher = context.getBean(XssWhitelistPathMatcher.class);
                    assertThat(matcher.getWhitelistPatterns()).hasSize(2);
                    assertThat(matcher.isWhitelisted("/api/content/articles")).isTrue();
                    assertThat(matcher.isWhitelisted("/swagger-ui/index.html")).isTrue();
                    assertThat(matcher.isWhitelisted("/api/admin/users")).isFalse();
                });
    }

    /**
     * 测试配置类，提供 WebFlux 必需的 Bean
     */
    @Configuration
    static class TestWebFluxConfiguration {

        @Bean
        public ServerCodecConfigurer serverCodecConfigurer() {
            return ServerCodecConfigurer.create();
        }

        @Bean
        public RequestedContentTypeResolver requestedContentTypeResolver() {
            return new org.springframework.web.reactive.accept.RequestedContentTypeResolverBuilder().build();
        }
    }
}
