package cn.com.chinastock.cnf.security.webflux.authorization;

import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.LogCategory;
import org.springframework.security.authorization.AuthorizationDecision;
import org.springframework.security.authorization.ReactiveAuthorizationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.server.authorization.AuthorizationContext;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Mono;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;

/**
 * 响应式 IP 白名单授权管理器
 * 
 * <p>该授权管理器用于在 WebFlux 环境下实现 IP 白名单访问控制，支持：</p>
 * <ul>
 *     <li>单个 IP 地址匹配</li>
 *     <li>CIDR 网段匹配</li>
 *     <li>IPv4 和 IPv6 地址支持</li>
 *     <li>X-Forwarded-For 和 X-Real-IP 头部支持</li>
 * </ul>
 * 
 */
public class ReactiveIpWhitelistAuthorizationManager implements ReactiveAuthorizationManager<AuthorizationContext> {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(ReactiveIpWhitelistAuthorizationManager.class);
    
    private final List<String> whitelist;

    public ReactiveIpWhitelistAuthorizationManager(List<String> whitelist) {
        this.whitelist = whitelist;
    }

    @Override
    public Mono<AuthorizationDecision> check(Mono<Authentication> authentication, AuthorizationContext context) {
        return Mono.fromCallable(() -> {
            String clientIp = getClientIpAddress(context);
            boolean allowed = isIpAllowed(clientIp);
            
            if (allowed) {
                logger.debug(LogCategory.FRAMEWORK_LOG, "IP {} is allowed by whitelist", clientIp);
            } else {
                logger.warn(LogCategory.FRAMEWORK_LOG, "IP {} is not in whitelist, access denied", clientIp);
            }
            
            return new AuthorizationDecision(allowed);
        });
    }

    /**
     * 获取客户端真实 IP 地址
     *
     * @param context 授权上下文
     * @return 客户端 IP 地址
     */
    private String getClientIpAddress(AuthorizationContext context) {
        var request = context.getExchange().getRequest();
        
        // 优先从 X-Forwarded-For 头获取
        String xForwardedFor = request.getHeaders().getFirst("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor)) {
            // X-Forwarded-For 可能包含多个 IP，取第一个
            String[] ips = xForwardedFor.split(",");
            if (ips.length > 0) {
                return ips[0].trim();
            }
        }
        
        // 从 X-Real-IP 头获取
        String xRealIp = request.getHeaders().getFirst("X-Real-IP");
        if (StringUtils.hasText(xRealIp)) {
            return xRealIp.trim();
        }
        
        // 从远程地址获取
        var remoteAddress = request.getRemoteAddress();
        if (remoteAddress != null) {
            return remoteAddress.getAddress().getHostAddress();
        }
        
        return "unknown";
    }

    /**
     * 检查 IP 是否在白名单中
     *
     * @param clientIp 客户端 IP
     * @return 如果在白名单中返回 true，否则返回 false
     */
    private boolean isIpAllowed(String clientIp) {
        if (!StringUtils.hasText(clientIp) || "unknown".equals(clientIp)) {
            return false;
        }
        
        for (String allowedIp : whitelist) {
            if (matchesIpPattern(clientIp, allowedIp)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 匹配 IP 模式
     *
     * @param clientIp 客户端 IP
     * @param pattern IP 模式（支持单个 IP 或 CIDR 网段）
     * @return 如果匹配返回 true，否则返回 false
     */
    private boolean matchesIpPattern(String clientIp, String pattern) {
        try {
            if (pattern.contains("/")) {
                // CIDR 网段匹配
                return matchesCidr(clientIp, pattern);
            } else {
                // 单个 IP 匹配 - 需要标准化 IP 地址进行比较
                return normalizeIpAddress(clientIp).equals(normalizeIpAddress(pattern));
            }
        } catch (Exception e) {
            logger.warn(LogCategory.FRAMEWORK_LOG, "Failed to match IP pattern: {} against {}", clientIp, pattern, e);
            return false;
        }
    }

    /**
     * 标准化 IP 地址
     *
     * @param ip IP 地址字符串
     * @return 标准化后的 IP 地址
     */
    private String normalizeIpAddress(String ip) {
        try {
            InetAddress address = InetAddress.getByName(ip);
            return address.getHostAddress();
        } catch (UnknownHostException e) {
            return ip;
        }
    }

    /**
     * CIDR 网段匹配
     *
     * @param clientIp 客户端 IP
     * @param cidr CIDR 网段表示法（如 ***********/24）
     * @return 如果在网段内返回 true，否则返回 false
     */
    private boolean matchesCidr(String clientIp, String cidr) throws UnknownHostException {
        String[] parts = cidr.split("/");
        if (parts.length != 2) {
            return false;
        }
        
        InetAddress clientAddress = InetAddress.getByName(clientIp);
        InetAddress networkAddress = InetAddress.getByName(parts[0]);
        int prefixLength = Integer.parseInt(parts[1]);
        
        // 检查地址族是否匹配
        if (clientAddress.getAddress().length != networkAddress.getAddress().length) {
            return false;
        }
        
        byte[] clientBytes = clientAddress.getAddress();
        byte[] networkBytes = networkAddress.getAddress();
        
        // 计算需要比较的字节数和位数
        int bytesToCompare = prefixLength / 8;
        int bitsToCompare = prefixLength % 8;
        
        // 比较完整字节
        for (int i = 0; i < bytesToCompare; i++) {
            if (clientBytes[i] != networkBytes[i]) {
                return false;
            }
        }
        
        // 比较剩余位
        if (bitsToCompare > 0 && bytesToCompare < clientBytes.length) {
            int mask = 0xFF << (8 - bitsToCompare);
            return (clientBytes[bytesToCompare] & mask) == (networkBytes[bytesToCompare] & mask);
        }
        
        return true;
    }
}
