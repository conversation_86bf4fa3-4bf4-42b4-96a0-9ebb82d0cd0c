package cn.com.chinastock.cnf.security.webflux;

import cn.com.chinastock.cnf.security.GalaxySecurityProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Galaxy Security WebFlux 扩展配置属性
 * 
 * <p>该类扩展了基础的 GalaxySecurityProperties，为 WebFlux 版本添加了额外的配置选项：</p>
 * <ul>
 *     <li>XSS 白名单路径配置</li>
 *     <li>其他 WebFlux 特有的安全配置</li>
 * </ul>
 * 
 */
@Component
@ConfigurationProperties(prefix = "galaxy.security")
public class GalaxySecurityWebfluxProperties extends GalaxySecurityProperties {

    private Xss xss = new Xss();

    public Xss getXss() {
        return xss;
    }

    public void setXss(Xss xss) {
        this.xss = xss;
    }

    /**
     * XSS 相关配置
     */
    public static class Xss {
        /**
         * XSS 白名单路径模式
         * 支持 Ant 风格路径匹配，如：/api/content/**
         */
        private List<String> whitelistPatterns;

        public List<String> getWhitelistPatterns() {
            return whitelistPatterns;
        }

        public void setWhitelistPatterns(List<String> whitelistPatterns) {
            this.whitelistPatterns = whitelistPatterns;
        }
    }
}
