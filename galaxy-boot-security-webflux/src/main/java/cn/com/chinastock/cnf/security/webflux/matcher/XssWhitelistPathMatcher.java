package cn.com.chinastock.cnf.security.webflux.matcher;

import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import java.util.List;

/**
 * XSS 白名单路径匹配器
 * 
 * <p>该匹配器用于判断请求路径是否在 XSS 过滤白名单中，支持：</p>
 * <ul>
 *     <li>Ant 风格路径匹配（如 /api/**）</li>
 *     <li>精确路径匹配</li>
 *     <li>多个白名单模式配置</li>
 * </ul>
 * 
 */
public class XssWhitelistPathMatcher {

    private final PathMatcher pathMatcher;
    private final List<String> whitelistPatterns;

    public XssWhitelistPathMatcher(List<String> whitelistPatterns) {
        this.pathMatcher = new AntPathMatcher();
        this.whitelistPatterns = whitelistPatterns;
    }

    /**
     * 检查路径是否在白名单中
     *
     * @param requestPath 请求路径
     * @return 如果在白名单中返回 true，否则返回 false
     */
    public boolean isWhitelisted(String requestPath) {
        if (whitelistPatterns == null || whitelistPatterns.isEmpty()) {
            return false;
        }
        
        if (requestPath == null) {
            return false;
        }
        
        for (String pattern : whitelistPatterns) {
            if (pathMatcher.match(pattern, requestPath)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 获取白名单模式列表
     *
     * @return 白名单模式列表
     */
    public List<String> getWhitelistPatterns() {
        return whitelistPatterns;
    }
}
