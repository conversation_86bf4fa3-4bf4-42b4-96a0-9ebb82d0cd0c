## Galaxy Boot Security WebFlux

> Galaxy Boot Security WebFlux 是基于 Spring Security WebFlux 的安全框架，专为响应式 Web 应用设计，提供了 XSS 防护、CSRF 防护、CORS 配置等企业级安全功能。

### 特性

- **响应式 XSS 防护**：支持请求和响应的 XSS 过滤，兼容 WebFlux 响应式流
- **CSRF 防护**：基于 WebFlux 的 CSRF 保护，支持白名单和黑名单配置
- **CORS 配置**：响应式 CORS 支持，灵活的跨域配置
- **Actuator 保护**：响应式 Actuator 端点安全保护
- **安全响应头**：自动配置安全相关的 HTTP 响应头
- **与现有组件集成**：复用 galaxy-boot-security 的配置属性

### 依赖配置

如果要在您的 WebFlux 项目中使用 `Galaxy Boot Security WebFlux`，需要引入对应的 starter：

```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-security-webflux</artifactId>
</dependency>
```

**注意**：该模块与 `galaxy-boot-security` 不能同时使用，因为它们分别针对不同的 Web 技术栈（WebFlux vs Servlet）。

### 配置说明

配置与 `galaxy-boot-security` 完全兼容，使用相同的配置属性：

```yaml
galaxy:
  security:
    input-protect: true   # 启用输入 XSS 防护
    output-protect: true  # 启用输出 XSS 防护
    user:                 # 默认用户配置
      name: user          # 用户名
      password: password  # 密码
    cors:                 # CORS 配置
      protect: true       
      whitelist:          # CORS 白名单
        - https://*.chinastock.com.cn
    csrf:                 # CSRF 配置
      protect: false      # 是否开启 CSRF 保护
      whitelist:          # CSRF 白名单（不需要保护的路径）
        - /api/public/**
      blacklist:          # CSRF 黑名单（强制需要保护的路径）
        - /api/admin/**
    actuator:             # Actuator 端点保护
      protect: true       # 是否开启保护
      whitelist:          # IP 白名单
        - 127.0.0.1
        - ***********/24
```
