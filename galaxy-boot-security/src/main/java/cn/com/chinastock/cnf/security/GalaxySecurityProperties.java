package cn.com.chinastock.cnf.security;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ConfigurationProperties(prefix="galaxy.security")
public class GalaxySecurityProperties {
    /**
     * 是否开启输入防护
     */
    private boolean inputProtect = false;
    /**
     * 是否开启输出防护
     */
    private boolean outputProtect = false;
    private Csrf csrf = new Csrf();
    private Cors cors = new Cors();
    private Actuator actuator = new Actuator();
    private User user = new User();
    private Xss xss = new Xss();

    public Csrf getCsrf() {
        return csrf;
    }

    public void setCsrf(Csrf csrf) {
        this.csrf = csrf;
    }

    public Cors getCors() {
        return cors;
    }

    public void setCors(Cors cors) {
        this.cors = cors;
    }

    public Actuator getActuator() {
        return actuator;
    }

    public void setActuator(Actuator actuator) {
        this.actuator = actuator;
    }

    public boolean isInputProtect() {
        return inputProtect;
    }

    public void setInputProtect(boolean inputProtect) {
        this.inputProtect = inputProtect;
    }

    public boolean isOutputProtect() {
        return outputProtect;
    }

    public void setOutputProtect(boolean outputProtect) {
        this.outputProtect = outputProtect;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Xss getXss() {
        return xss;
    }

    public void setXss(Xss xss) {
        this.xss = xss;
    }

    public static class Csrf {
        /**
         * 开启 CSRF 防护
         */
        private boolean protect = false;
        private List<String> whitelist;
        private List<String> blacklist;

        public boolean isProtect() {
            return protect;
        }

        public void setProtect(boolean protect) {
            this.protect = protect;
        }

        public List<String> getWhitelist() {
            return whitelist;
        }

        public void setWhitelist(List<String> whitelist) {
            this.whitelist = whitelist;
        }

        public List<String> getBlacklist() {
            return blacklist;
        }

        public void setBlacklist(List<String> blacklist) {
            this.blacklist = blacklist;
        }
    }

    public static class Cors {
        /**
         * 开启跨域保护
         */
        private boolean protect = false;
        /**
         * 跨域白名单
         */
        private List<String> whitelist;

        public boolean isProtect() {
            return protect;
        }

        public void setProtect(boolean protect) {
            this.protect = protect;
        }

        public List<String> getWhitelist() {
            return whitelist;
        }

        public void setWhitelist(List<String> whitelist) {
            this.whitelist = whitelist;
        }
    }

    public static class Actuator {
        /**
         * 开启 Actuator 保护
         */
        private boolean protect = false;
        /**
         * Actuator IP 白名单
         */
        private List<String> whitelist;

        public boolean isProtect() {
            return protect;
        }

        public void setProtect(boolean protect) {
            this.protect = protect;
        }

        public List<String> getWhitelist() {
            return whitelist;
        }

        public void setWhitelist(List<String> whitelist) {
            this.whitelist = whitelist;
        }
    }

    public static class User {
        private String name;
        private String password;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }

    public static class Xss {
        /**
         * XSS 白名单路径模式
         */
        private List<String> whitelistPatterns;

        public List<String> getWhitelistPatterns() {
            return whitelistPatterns;
        }

        public void setWhitelistPatterns(List<String> whitelistPatterns) {
            this.whitelistPatterns = whitelistPatterns;
        }
    }
}